package handlers

import (
	"github.com/labstack/echo/v4"
)

// UserHandlerInterface 用户处理器接口
type UserHandlerInterface interface {
	Register(c echo.Context) error
	Login(c echo.Context) error
	ResetPassword(c echo.Context) error
	VerifyResetToken(c echo.Context) error
	CompletePasswordReset(c echo.Context) error
	GetProfile(c echo.Context) error
	UpdateProfile(c echo.Context) error
	ChangePassword(c echo.Context) error
	ListUsers(c echo.Context) error
	DisableAccount(c echo.Context) error
}

// ESIMHandlerInterface eSIM处理器接口
type ESIMHandlerInterface interface {
	ListProviders(c echo.Context) error
	ListPackages(c echo.Context) error
	GetPackageDetails(c echo.Context) error
	ListSupportedRegions(c echo.Context) error
	GetESIMDetails(c echo.Context) error
	ListESIMs(c echo.Context) error
	UpdateESIMStatus(c echo.Context) error
	AddESIMData(c echo.Context) error
	SendESIMMessage(c echo.Context) error
	GetESIMUsage(c echo.Context) error
	GetAccountBalance(c echo.Context) error
	HandleWebhook(c echo.Context) error
	ListUserESIMs(c echo.Context) error
}

// OrderHandlerInterface 订单处理器接口
type OrderHandlerInterface interface {
	CreateOrder(c echo.Context) error
	GetOrder(c echo.Context) error
	ListOrders(c echo.Context) error
	UpdateOrderStatus(c echo.Context) error
	ListUserOrders(c echo.Context) error
}

// PromotionHandlerInterface 促销处理器接口
type PromotionHandlerInterface interface {
	ValidatePromotion(c echo.Context) error
	ApplyPromotion(c echo.Context) error
	CreatePromotion(c echo.Context) error
	GetPromotion(c echo.Context) error
	ListPromotions(c echo.Context) error
	UpdatePromotion(c echo.Context) error
	DeletePromotion(c echo.Context) error
	// 代理商促销码管理接口
	ListResellerPromotions(c echo.Context) error
	CreateResellerPromotion(c echo.Context) error
	GetResellerPromotion(c echo.Context) error
	UpdateResellerPromotion(c echo.Context) error
	DeleteResellerPromotion(c echo.Context) error
}

// ResellerHandlerInterface 代理商处理器接口
type ResellerHandlerInterface interface {
	// 管理员接口
	CreateReseller(c echo.Context) error
	GetReseller(c echo.Context) error
	UpdateReseller(c echo.Context) error
	UpdateResellerStatus(c echo.Context) error
	RegenerateAPIKey(c echo.Context) error
	AddResellerBalance(c echo.Context) error
	ListResellers(c echo.Context) error
	// 代理商自身管理接口
	GetResellerProfile(c echo.Context) error
	UpdateResellerProfile(c echo.Context) error
	GetResellerBalance(c echo.Context) error
	// 代理商用户管理接口
	ListResellerUsers(c echo.Context) error
	CreateResellerUser(c echo.Context) error
	GetResellerUser(c echo.Context) error
	GetResellerUserESIMs(c echo.Context) error
	GetResellerUserOrders(c echo.Context) error
}

// EnterpriseHandlerInterface 企业代理商处理器接口
type EnterpriseHandlerInterface interface {
	// 员工管理
	ListEmployees(c echo.Context) error
	CreateEmployee(c echo.Context) error
	GetEmployee(c echo.Context) error
	UpdateEmployee(c echo.Context) error
	DeleteEmployee(c echo.Context) error
	// 部门管理
	ListDepartments(c echo.Context) error
	CreateDepartment(c echo.Context) error
	GetDepartment(c echo.Context) error
	UpdateDepartment(c echo.Context) error
	DeleteDepartment(c echo.Context) error
	// eSIM管理
	AssignESIMs(c echo.Context) error
	ReclaimESIMs(c echo.Context) error
	GetEmployeeESIMs(c echo.Context) error
	// 管理员接口
	ListEnterprises(c echo.Context) error
	GetEnterprise(c echo.Context) error
	UpdateEnterprise(c echo.Context) error
}

// CreditHandlerInterface 积分处理器接口
type CreditHandlerInterface interface {
	GetUserCredit(c echo.Context) error
	GetTransactionHistory(c echo.Context) error
	GetTransactionDetail(c echo.Context) error
	AddCredit(c echo.Context) error
	AdminAddUserCredit(c echo.Context) error
	AddResellerUserCredit(c echo.Context) error
}

// RateHandlerInterface 费率处理器接口
type RateHandlerInterface interface {
	// 管理员接口
	ListGlobalRates(c echo.Context) error
	CreateGlobalRate(c echo.Context) error
	UpdateGlobalRate(c echo.Context) error
	DeleteGlobalRate(c echo.Context) error
	// 代理商接口
	ListResellerRates(c echo.Context) error
	CreateResellerRate(c echo.Context) error
	// 企业代理商接口
	ListEnterpriseRates(c echo.Context) error
	CreateEnterpriseRate(c echo.Context) error
}
