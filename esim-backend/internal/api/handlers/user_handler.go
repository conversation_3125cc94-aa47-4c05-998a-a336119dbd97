package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/service"
	"vereal/letsesim/pkg/jwt"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService  *service.UserService
	tokenService *jwt.TokenService
}

// NewUserHandler 创建新的用户处理器
func NewUserHandler(userService *service.UserService, tokenService *jwt.TokenService) *UserHandler {
	return &UserHandler{
		userService:  userService,
		tokenService: tokenService,
	}
}

// Register 用户注册
func (h *UserHandler) Register(c echo.Context) error {
	// 解析请求
	var req struct {
		Email    string `json:"email" validate:"required,email"`
		Password string `json:"password" validate:"required,min=8"`
		Name     string `json:"name,omitempty"`
		Username string `json:"username,omitempty"` // 添加username字段以兼容前端
		Mobile   string `json:"mobile,omitempty" validate:"omitempty,numeric,min=10,max=15"`
		Phone    string `json:"phone,omitempty"`    // 添加phone字段以兼容前端
		Nickname string `json:"nickname,omitempty"` // 添加nickname字段以兼容前端
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "Email") {
			errMsg = "邮箱格式无效"
		} else if strings.Contains(errMsg, "Password") && strings.Contains(errMsg, "min=8") {
			errMsg = "密码至少需要8个字符"
		} else if strings.Contains(errMsg, "Mobile") {
			errMsg = "手机号码格式无效"
		}

		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 优先使用username字段作为名称，如果存在
	name := req.Name
	if req.Username != "" {
		name = req.Username
	}
	// 优先使用nickname字段，如果存在
	if req.Nickname != "" {
		name = req.Nickname
	}

	// 优先使用mobile字段，如果存在，否则使用phone字段
	mobile := req.Mobile
	if mobile == "" && req.Phone != "" {
		mobile = req.Phone
	}

	// 创建用户
	newUser, err := h.userService.CreateUser(
		c.Request().Context(),
		req.Email,
		req.Password,
		name,
		mobile,
		user.RoleUser,
	)
	if err != nil {
		if strings.Contains(err.Error(), "email already exists") {
			return echo.NewHTTPError(http.StatusConflict, map[string]interface{}{
				"code":    409,
				"message": "该邮箱已被注册",
			})
		}
		return err
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":        newUser.ID,
		"email":     newUser.Email,
		"name":      newUser.Name,
		"createdAt": newUser.CreatedAt,
	})
}

// Login 用户登录
func (h *UserHandler) Login(c echo.Context) error {
	// 解析请求
	var req struct {
		Email    string `json:"email" validate:"required,email"`
		Password string `json:"password" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "Email") {
			errMsg = "邮箱格式无效"
		}

		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 验证用户
	user, err := h.userService.Authenticate(c.Request().Context(), req.Email, req.Password)
	if err != nil {
		if strings.Contains(err.Error(), "user not found") || strings.Contains(err.Error(), "password incorrect") {
			return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
				"code":    401,
				"message": "邮箱或密码错误",
			})
		}
		return err
	}

	// 检查用户状态
	if !user.IsActive() {
		return echo.NewHTTPError(http.StatusForbidden, map[string]interface{}{
			"code":    403,
			"message": "账户已被禁用，请联系管理员",
		})
	}

	// 生成JWT令牌
	token, err := h.tokenService.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		return err
	}

	// 更新最后登录时间
	// TODO: 增加更新最后登录时间的功能

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"token": token,
		"user": map[string]interface{}{
			"id":    user.ID,
			"email": user.Email,
			"name":  user.Name,
			"role":  user.Role,
		},
	})
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 返回用户资料
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":          user.ID,
		"email":       user.Email,
		"name":        user.Name,
		"mobile":      user.Mobile,
		"role":        user.Role,
		"status":      user.Status,
		"createdAt":   user.CreatedAt,
		"lastLoginAt": user.LastLoginAt,
	})
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 解析请求
	var req struct {
		Name     string `json:"name"`
		Nickname string `json:"nickname"` // 添加nickname字段以兼容测试
		Mobile   string `json:"mobile" validate:"omitempty,numeric,min=10,max=15"`
		Phone    string `json:"phone"` // 添加phone字段以兼容测试
		Role     string `json:"role,omitempty"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	if req.Role != "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Role field update is not allowed")
	}

	// 优先使用nickname字段，如果存在
	name := req.Name
	if req.Nickname != "" {
		name = req.Nickname
	}

	// 优先使用mobile字段，如果存在，否则使用phone字段
	mobile := req.Mobile
	if mobile == "" && req.Phone != "" {
		mobile = req.Phone
	}

	if err := c.Validate(&req); err != nil {
		// 为了提供更明确的错误消息
		errMsg := err.Error()
		if strings.Contains(errMsg, "Mobile") ||
			strings.Contains(errMsg, "numeric") ||
			(mobile != "" && !isValidMobile(mobile)) {
			errMsg = "Invalid mobile phone format. Mobile number must be numeric and 10-15 digits."
		}
		return echo.NewHTTPError(http.StatusBadRequest, errMsg)
	}

	updatedUser, err := h.userService.UpdateUser(c.Request().Context(), user.ID, name, mobile)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":       updatedUser.ID,
		"email":    updatedUser.Email,
		"name":     updatedUser.Name,
		"mobile":   updatedUser.Mobile,
		"nickname": updatedUser.Name, // 设置nickname等于name以兼容测试
	})
}

// isValidMobile 验证手机号码格式是否有效
func isValidMobile(mobile string) bool {
	// 简单验证：只包含数字且长度在10-15之间
	if len(mobile) < 10 || len(mobile) > 15 {
		return false
	}
	for _, c := range mobile {
		if c < '0' || c > '9' {
			return false
		}
	}
	return true
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 解析请求
	var req struct {
		CurrentPassword string `json:"currentPassword" validate:"required"`
		NewPassword     string `json:"newPassword" validate:"required,min=8"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "NewPassword") && strings.Contains(errMsg, "min=8") {
			errMsg = "密码至少需要8个字符"
		}
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 修改密码
	err := h.userService.ChangePassword(c.Request().Context(), user.ID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		// 处理特定错误类型
		if strings.Contains(err.Error(), "password incorrect") {
			return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
				"code":    401,
				"message": "当前密码不正确",
			})
		}
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "密码修改成功",
	})
}

// ListUsers 获取用户列表 (Admin)
func (h *UserHandler) ListUsers(c echo.Context) error {
	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filter := make(map[string]interface{})
	if role := c.QueryParam("role"); role != "" {
		filter["role"] = role
	}

	if status := c.QueryParam("status"); status != "" {
		filter["status"] = status
	}

	// 获取用户列表
	users, total, err := h.userService.ListUsers(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	userList := make([]map[string]interface{}, len(users))
	for i, user := range users {
		userList[i] = map[string]interface{}{
			"id":        user.ID,
			"email":     user.Email,
			"name":      user.Name,
			"role":      user.Role,
			"status":    user.Status,
			"createdAt": user.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"users": userList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// ResetPassword 重置用户密码
func (h *UserHandler) ResetPassword(c echo.Context) error {
	// 解析请求
	var req struct {
		Email string `json:"email" validate:"required,email"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "Email") {
			errMsg = "邮箱格式无效"
		}
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 请求密码重置
	err := h.userService.RequestPasswordReset(c.Request().Context(), req.Email)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message":   "重置密码邮件已发送到您的邮箱",
		"expiresIn": 3600, // 重置链接有效期（秒）
	})
}

// VerifyResetToken 验证密码重置令牌
func (h *UserHandler) VerifyResetToken(c echo.Context) error {
	// 获取令牌
	token := c.QueryParam("token")
	if token == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "未提供重置令牌")
	}

	// 验证令牌
	email, valid, err := h.userService.VerifyResetToken(c.Request().Context(), token)
	if err != nil {
		return err
	}

	// 返回验证结果
	return c.JSON(http.StatusOK, map[string]interface{}{
		"valid": valid,
		"email": email,
	})
}

// CompletePasswordReset 完成密码重置
func (h *UserHandler) CompletePasswordReset(c echo.Context) error {
	// 解析请求
	var req struct {
		Token    string `json:"token" validate:"required"`
		Password string `json:"password" validate:"required,min=8"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 完成密码重置
	err := h.userService.CompletePasswordReset(c.Request().Context(), req.Token, req.Password)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message":  "密码重置成功",
		"canLogin": true,
	})
}

// DisableAccount 禁用/注销用户账号
func (h *UserHandler) DisableAccount(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 禁用用户账号
	err := h.userService.DisableUser(c.Request().Context(), user.ID)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "账号已成功禁用",
	})
}
