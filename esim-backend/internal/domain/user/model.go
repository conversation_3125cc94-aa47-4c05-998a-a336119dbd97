package user

import (
	"time"
)

// Role 用户角色
type Role string

const (
	RoleAdmin      Role = "ADMIN"
	RoleUser       Role = "USER"
	RoleReseller   Role = "RESELLER"
	RoleEnterprise Role = "ENTERPRISE"
)

// User 用户模型
type User struct {
	ID             string     `json:"id"`
	Email          string     `json:"email"`
	HashedPassword string     `json:"-"` // 不输出到JSON
	Name           string     `json:"name"`
	Mobile         string     `json:"mobile,omitempty"`
	Role           Role       `json:"role"`
	Status         string     `json:"status"`
	ResellerID     string     `json:"resellerId,omitempty"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt"`
	LastLoginAt    time.Time  `json:"lastLoginAt,omitempty"`
	DeletedAt      *time.Time `json:"deletedAt,omitempty"`
}

// UserStatus 用户状态
const (
	UserStatusActive   = "ACTIVE"
	UserStatusInactive = "INACTIVE"
	UserStatusLocked   = "LOCKED"
)

// NewUser 创建新用户
func NewUser(email, name string, role Role, resellerID string) *User {
	now := time.Now()
	return &User{
		Email:      email,
		Name:       name,
		Role:       role,
		Status:     UserStatusActive,
		ResellerID: resellerID,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
}

// IsAdmin 检查用户是否是管理员
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsReseller 检查用户是否是代理商
func (u *User) IsReseller() bool {
	return u.Role == RoleReseller
}

// IsEnterprise 检查用户是否是企业代理商
func (u *User) IsEnterprise() bool {
	return u.Role == RoleEnterprise
}

// IsActive 检查用户是否处于活动状态
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// BelongsToReseller 检查用户是否归属某代理商
func (u *User) BelongsToReseller(resellerID string) bool {
	return u.ResellerID == resellerID
}
