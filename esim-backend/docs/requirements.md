# AuraESIM 前后端需求文档

 

本文档详细描述了AuraESIM平台的前后端功能需求和技术架构，旨在为个人用户、代理商、企业代理商和管理员提供一个完整的eSIM服务平台。基于Clean架构设计，本文档涵盖了项目概述、架构设计、后端API服务需求、用户端App需求和后台管理网站（AdminPanel）需求。

## 目录

- [项目概述](#%E9%A1%B9%E7%9B%AE%E6%A6%82%E8%BF%B0)
- [架构设计](#%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1)
- [后端API服务需求](#%E5%90%8E%E7%AB%AFAPI%E6%9C%8D%E5%8A%A1%E9%9C%80%E6%B1%82)
- [用户端App需求](#%E7%94%A8%E6%88%B7%E7%AB%AFApp%E9%9C%80%E6%B1%82)
- [后台管理网站（AdminPanel）需求](#%E5%90%8E%E5%8F%B0%E7%AE%A1%E7%90%86%E7%BD%91%E7%AB%99%EF%BC%88AdminPanel%EF%BC%89%E9%9C%80%E6%B1%82)
- [接口对接](#%E6%8E%A5%E5%8F%A3%E5%AF%B9%E6%8E%A5)

## 项目概述

AuraESIM是一个eSIM服务平台，旨在为用户提供便捷的eSIM购买、激活和管理功能，同时支持代理商和企业代理商的业务运营，以及平台管理员的全面管理。平台支持多种用户角色，每种角色具有特定的功能和权限，确保用户体验和业务需求得到满足。

### 目标用户

- **个人用户 (User)**：购买和使用eSIM，管理个人资料和订单，通过用户端App访问服务。
- **代理商 (Reseller)**：管理下属用户、eSIM分配、余额和促销活动，通过后台管理网站（AdminPanel）操作。
- **企业代理商 (Enterprise)**：在代理商功能基础上，管理部门和员工，支持批量eSIM分配，通过后台管理网站（AdminPanel）操作。
- **管理员 (Admin)**：管理整个平台，包括用户、代理商、费率和促销，通过后台管理网站（AdminPanel）操作。

### 核心功能

- **eSIM管理**：支持eSIM的购买、激活、充值、暂停和回收。
- **订单处理**：支持订单创建、支付、查看和取消。
- **积分系统**：支持用户积分充值、消费和交易记录查看。
- **促销活动**：支持促销码的创建、验证和应用。
- **费率管理**：支持为代理商和企业代理商设置不同的费率，也支持他们为他们销售给自己用户的产品设置自定义费率。
- **用户管理**：支持不同角色的用户注册、登录和权限控制。

### 系统组件

- **用户端App**：面向个人用户的移动应用，提供eSIM购买和管理功能，注重极致用户体验。
- **后台管理网站（AdminPanel）**：面向代理商、企业代理商和管理员的响应式Web应用，用于管理功能。
- **后端API服务**：支持所有前端应用的数据交互和业务逻辑处理。

## 架构设计

AuraESIM平台采用Clean架构设计，确保关注点分离和依赖倒置，以提高代码的可维护性和可扩展性。架构分为以下层次：

### 领域层 (Domain Layer)

- 包含核心业务实体和逻辑，如用户、eSIM、订单、代理商和企业模型。
- 定义了数据结构和业务规则，例如eSIM状态检查、订单状态更新和佣金计算。
- 独立于任何外部框架或技术，确保核心逻辑的纯粹性。

### 应用层 (Application Layer)

- 包含业务用例和流程逻辑，协调领域层实体和外部接口。
- 实现服务逻辑，如订单创建、eSIM激活和用户认证。
- 通过接口与外部层交互，遵循依赖倒置原则。

### 接口层 (Interface Layer)

- 包含API处理器、HTTP路由和中间件，处理外部请求和响应。
- 定义了各模块的处理器接口（如`UserHandler`、`ESIMHandler`），确保高层模块不依赖低层实现。
- 负责用户认证、日志记录和错误处理。

### 基础设施层 (Infrastructure Layer)

- 包含数据库实现、外部API集成和配置管理。
- 实现数据持久化逻辑，如Postgres数据库操作。
- 提供外部服务接口，如eSIM提供商的API调用。

这种分层设计确保了核心业务逻辑与外部依赖的解耦，使得系统易于测试和扩展。

## 后端API服务需求

后端系统负责处理业务逻辑、数据存储和API服务，支持不同角色的功能需求。以下是各模块的详细需求：

### 用户管理

- **功能**： 
  - 用户注册、登录和密码重置。
  - 个人资料查看和更新。
  - 支持用户角色（个人用户、代理商、企业代理商、管理员）和状态管理。
- **API端点**： 
  - `POST /api/v1/register`：用户注册。
  - `POST /api/v1/login`：用户登录，返回JWT令牌。
  - `GET /api/v1/user/profile`：获取用户资料。
  - `PUT /api/v1/user/profile`：更新用户资料。

### eSIM管理

- **功能**： 
  - 提供商列表、套餐查询和区域支持查看。
  - eSIM购买、激活、充值、暂停、恢复和撤销。
  - eSIM使用情况和账户余额查询。
- **API端点**： 
  - `GET /api/v1/esim/providers`：获取eSIM提供商列表。
  - `GET /api/v1/esim/provider/:providerType/packages`：获取套餐列表。
  - `POST /api/v1/esim/provider/:providerType/esim/:action`：管理eSIM状态。
  - `GET /api/v1/esim/provider/:providerType/usage`：获取eSIM使用情况。

### 订单管理

- **功能**： 
  - 订单创建、查看、列表查询和取消。
  - 支持订单关联eSIM和促销码应用。
- **API端点**： 
  - `POST /api/v1/order`：创建订单。
  - `GET /api/v1/order/:id`：获取订单详情。
  - `POST /api/v1/order/:id/cancel`：取消订单。

### 积分管理

- **功能**： 
  - 积分余额查询、充值和交易历史查看。
  - 支持管理员和代理商为用户充值积分。
- **API端点**： 
  - `GET /api/v1/credit`：获取用户积分余额。
  - `POST /api/v1/credit/topup`：用户积分充值。
  - `POST /api/v1/admin/user/:id/credit`：管理员为用户充值积分。

### 促销管理

- **功能**： 
  - 促销码验证和应用。
  - 代理商和管理员创建和管理促销活动。
- **API端点**： 
  - `POST /api/v1/promotion/validate`：验证促销码。
  - `POST /api/v1/admin/promotion`：创建促销活动。

### 代理商管理

- **功能**： 
  - 代理商资料管理、余额查询和用户管理。
  - 支持促销码和费率自定义。
- **API端点**： 
  - `GET /api/v1/reseller/profile`：获取代理商资料。
  - `GET /api/v1/reseller/users`：获取下属用户列表。
  - `POST /api/v1/reseller/rate`：设置费率。

### 企业代理商管理

- **功能**： 
  - 部门和员工管理。
  - 批量eSIM分配和回收。
  - 自定义费率管理：费率仅对类似代理商角色的用户生效；对于企业员工，超出分配额度的使用按自定义费率计费。
- **API端点**： 
  - `POST /api/v1/enterprise/employee`：创建员工。
  - `POST /api/v1/enterprise/esims/assign`：批量分配eSIM。
  - `POST /api/v1/enterprise/rate`：设置自定义费率。

### 管理员管理

- **功能**： 
  - 全平台用户、代理商、促销和费率管理。
  - 支持API密钥重新生成和余额调整。
- **API端点**： 
  - `GET /api/v1/admin/users`：获取用户列表。
  - `POST /api/v1/admin/reseller`：创建代理商。

### 认证与权限

- **功能**： 
  - 支持JWT认证和API密钥认证（代理商/企业代理商）。
  - 不同角色访问不同API端点，确保数据隔离。
- **中间件**： 
  - `UserAuth()`：个人用户认证。
  - `ResellerAuth()`：代理商认证。
  - `EnterpriseAuth()`：企业代理商认证。
  - `AdminAuth()`：管理员认证。

## 用户端App需求

用户端App面向个人用户，提供eSIM购买和管理功能，注重极致用户体验。 App使用React Native及相关技术栈实现，通过一套代码，提供Webapp，ios及android平台原生App。 以下是详细设计：

### 整体设计原则

- **极致体验**：界面简洁直观，操作流畅，响应迅速。
- **响应式设计**：适配不同屏幕尺寸的移动设备。
- **多语言支持**：支持中文、日文和英文界面，方便不同地区用户。
- **安全性**：用户数据加密传输，防止未授权访问。

### 页面与功能设计

#### 首页

- **轮播卡片**： 
  - 位于页面顶部，展示特色介绍、功能介绍和各类优惠。
  - 自动播放，支持手动滑动，可点击进入相应页面。
- **推荐地区**： 
  - 以卡片形式展示（支持手动左右滑动）。
  - 包含国家或地区筛选条件入口，点击后进入相应eSIM数据套餐卡片列表页面。
- **按大洲分组的国家入口**： 
  - 以小卡片形式展示（包括国旗和国家名），点击后进入该国家的eSIM数据套餐卡片列表页面。
  - 列表上部有一行小字体快捷跳转文字链接（格式：多地区 | 北美洲 | 欧洲 | 亚洲 | 非洲 | 大洋洲 | 南美洲），默认高亮“多地区”。
  - 点击快捷跳转链接，页面快速滚动到相应分组开头。
  - 快捷跳转条在用户滚动后始终置顶悬浮显示，返回到顶部位置后自动还原。

#### eSIM页面

- **选项卡**： 
  - 包含“有效”和“已过期”两个选项卡，切换显示不同状态的eSIM。
- **eSIM数据套餐列表**： 
  - 以大卡片形式展示每个eSIM数据套餐。
  - **卡片顶部（小字体）**： 
    - 左侧：图标和使用状态标签文字（待激活/使用中/暂停）。
    - 右侧：订单或套餐ID（灰色字体），如#1234567890。
  - **卡片主体**： 
    - 第一行：左侧显示eSIM数据套餐名称，右侧显示“查看详情”文字按钮。
    - 第二行：纵向显示块（剩余流量：xxGB），纵向显示块（剩余时间：xx天xx小时）。
    - 第三行：“充流量”按钮，支持快速充值流量。
- **操作功能**： 
  - 支持eSIM激活（通过二维码或激活码）。
  - 支持查看eSIM使用情况和状态变更操作（如暂停、恢复）。

#### 我的页面

- **用户资料**： 
  - 顶部显示圆形用户头像（Avatar）和用户名称，点击可编辑个人资料。
- **账户与积分**： 
  - 显示账户积分余额，右侧“详情”按钮查看积分流水记录。
  - “充值”按钮，支持积分充值。
- **邀请与优惠**： 
  - “个人邀请码”：点击后查看邀请码，并可发起邀请。
  - “优惠码”：点击后输入优惠码兑换奖励。
- **帮助与支持**： 
  - “常见问题”：查看常见问题解答。
  - “联系我们”：支持邮件反馈问题，可选择问题类型、关联订单、填写问题描述后提交。
  - “关于我们”：显示平台信息和版本号。

### 通用设计要求

- **导航**：底部导航栏包含“首页”、“eSIM”和“我的”三个主要入口，快速切换页面。
- **搜索功能**：支持在eSIM页面和首页搜索国家、地区或套餐。
- **操作便利**：优先使用底部弹窗来在当前页面快捷处理功能。
- **通知系统**：支持推送eSIM状态变更、订单更新和优惠活动通知。
- **用户反馈**：提供操作反馈（如成功提示、错误警告），确保用户了解操作结果。

## 后台管理网站（AdminPanel）需求

后台管理网站（AdminPanel）面向代理商、企业代理商和管理员，提供管理功能，支持响应式布局，适配桌面和移动端访问。以下是详细设计：

### 整体设计原则

- **精炼易用**：界面设计以最佳实践为导向，功能精炼，操作高效。
- **响应式设计**：支持桌面和移动端，确保在不同设备上的一致体验。
- **多语言支持**：支持中文和英文界面。
- **安全性**：用户数据加密传输，严格权限控制，防止未授权访问。

### 代理商角色功能设计

- **仪表板**： 
  - 显示余额、下属用户数量、eSIM分配情况和最近活动概览。
- **用户管理**： 
  - 列表显示下属用户，支持搜索、筛选（状态、注册时间）。
  - 支持创建新用户，分配eSIM，查看用户详情（包括订单和eSIM列表）。
  - 支持批量操作，如批量发送通知或调整积分。
- **eSIM管理**： 
  - 列表显示所有eSIM，支持筛选（状态、用户、套餐类型）。
  - 支持分配eSIM给用户，查看eSIM使用情况，执行状态变更操作（暂停、恢复、撤销）。
- **促销管理**： 
  - 列表显示促销码，支持搜索和状态筛选。
  - 支持创建新促销码，设置折扣类型、有效期和使用限制。
  - 支持查看促销码使用情况，编辑或删除促销码。
- **费率管理**： 
  - 列表显示自定义费率，支持按套餐、国家筛选。
  - 支持设置新费率，调整现有费率，确保费率仅对下属用户生效。
- **财务管理**： 
  - 显示余额和交易记录，支持按时间范围筛选。
  - 支持查看佣金收入明细。
- **个人资料**： 
  - 查看和编辑代理商资料，修改密码，设置回调URL。

### 企业代理商角色功能设计

- **仪表板**： 
  - 显示员工数量、部门结构、eSIM分配概况和最近活动概览。
- **组织管理**： 
  - **部门管理**： 
    - 列表显示部门，支持搜索和排序。
    - 支持创建、编辑和删除部门，查看部门下员工列表。
  - **员工管理**： 
    - 列表显示员工，支持搜索、筛选（部门、状态）。
    - 支持添加新员工，分配部门和职位，查看员工详情（包括eSIM分配情况）。
    - 支持批量操作，如批量添加员工或调整部门。
- **eSIM管理**： 
  - 列表显示所有eSIM，支持筛选（状态、员工、部门、套餐类型）。
  - 支持批量分配eSIM到员工或部门，批量回收未使用的eSIM。
  - 支持查看eSIM使用情况，执行状态变更操作。
  - 支持设置员工eSIM使用额度，超出额度的使用按自定义费率计费。
- **促销管理**：同代理商角色，支持创建和管理促销码。
- **费率管理**： 
  - 列表显示自定义费率，支持按套餐、国家筛选。
  - 支持设置新费率，调整现有费率，费率仅对类似代理商角色的用户生效，员工超出额度计费时按此费率计算。
- **财务管理**：同代理商角色，支持余额和交易记录查看。
- **个人资料**：同代理商角色，支持资料编辑和密码修改。

### 管理员角色功能设计

- **仪表板**： 
  - 显示平台用户总数、代理商数量、订单统计和系统运行状态概览。
- **用户管理**： 
  - 列表显示所有用户，支持搜索、筛选（角色、状态、注册时间）。
  - 支持查看用户详情，修改角色和状态，调整积分余额。
- **代理商管理**： 
  - 列表显示代理商和企业代理商，支持搜索、筛选（状态、类型）。
  - 支持创建新代理商，审核代理商申请，查看详情（包括下属用户和财务情况）。
  - 支持调整代理商余额，重新生成API密钥，设置佣金率。
- **促销管理**： 
  - 列表显示所有促销活动，支持搜索、筛选（状态、范围、代理商）。
  - 支持创建平台级促销活动，设置折扣类型、有效期和使用限制。
  - 支持查看促销使用情况，编辑或删除促销活动。
- **费率管理**： 
  - 列表显示全局费率和代理商自定义费率，支持按套餐、国家筛选。
  - 支持设置全局费率标准，审核代理商自定义费率。
- **财务管理**： 
  - 显示平台财务总览，包括收入、支出和佣金分配情况。
  - 支持查看详细交易记录，按时间、用户、代理商筛选。
- **系统监控**： 
  - 显示平台运行状态，包括API响应时间、错误率和服务器负载。
  - 支持查看错误日志，按时间、类型筛选。
- **个人资料**： 
  - 查看和编辑管理员资料，修改密码。

### 通用设计要求

- **导航**：左侧或顶部导航栏，包含主要功能入口，支持快速切换模块。
- **搜索与筛选**：所有列表页面支持搜索和多条件筛选，提升数据查找效率。
- **批量操作**：支持对用户、eSIM、订单等进行批量操作，如批量分配、状态变更。
- **数据可视化**：使用图表展示统计数据，如用户增长趋势、eSIM使用情况、财务收入分布。
- **通知系统**：支持向用户或代理商发送通知，如审核结果、余额不足提醒。
- **用户反馈**：提供操作反馈（如成功提示、错误警告），确保用户了解操作结果。

## 接口对接

前后端通过RESTful API进行数据交互，所有API请求和响应遵循JSON格式。以下是关键接口对接要求：

### 认证流程

- 用户登录后获取JWT令牌，所有后续请求需在`Authorization`头中携带`Bearer {token}`。
- 代理商和企业代理商的服务端对接需额外提供`X-API-Key`和`X-API-Secret`头。

### 错误处理

- 所有API错误返回统一格式：`{"code": 400, "message": "错误信息"}`。
- 前端需根据错误代码和消息提供用户友好的提示。

### 分页与过滤

- 列表查询API支持分页参数`page`和`pageSize`，以及状态和日期范围过滤。
- 前端需实现分页控件和过滤条件输入框。

### 实时更新

- 对于eSIM状态和订单状态变化，前端需通过轮询或WebSocket实现实时更新。
- 后端需提供状态变更的Webhook支持，通知前端或代理商系统。

### 数据格式

- 金额字段统一使用整数（单位：分）或浮点数（单位：元），避免精度问题。
- 日期时间字段统一使用ISO 8601格式（如`2023-07-20T17:01:37+0000`）。

通过以上需求设计，AuraESIM平台将能够满足不同角色用户的需求，提供高效、安全和用户友好的eSIM服务体验。本文档将作为开发和测试的依据，确保项目目标的实现，并支持后续直接进入编码实现阶段。