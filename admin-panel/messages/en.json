{"common": {"loading": "Loading...", "submit": "Submit", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "save": "Save", "search": "Search", "reset": "Reset", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "view": "View", "copy": "Copy", "download": "Download", "upload": "Upload", "select": "Select", "selectAll": "Select All", "clear": "Clear", "filter": "Filter", "sort": "Sort", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}, "auth": {"login": "Sign in", "logout": "Logout", "username": "Username", "password": "Password", "email": "Email", "rememberMe": "Remember Me", "forgotPassword": "No account yet? Sign up", "loginTitle": "Welcome back", "loginSubtitle": "Please enter your details.", "invalidCredentials": "Invalid email or password", "accountDisabled": "Account has been disabled, please contact administrator", "tooManyAttempts": "Too many login attempts, please try again later", "serverError": "Server error, please try again later", "networkError": "Network connection error, please check your connection", "unknownError": "<PERSON><PERSON> failed, please try again later", "loginRequired": "Please login first", "sessionExpired": "Session expired, please login again", "unauthorized": "You don't have permission to access this page", "accessDenied": "Access Denied"}, "navigation": {"dashboard": "Dashboard", "overview": "Overview", "analytics": "Analytics", "esim": "eSIM Management", "orders": "Order Management", "users": "User Management", "resellers": "Reseller Management", "enterprises": "Enterprise Management", "promotions": "Promotion Management", "rates": "Rate Management", "credits": "Credit Management", "reports": "Report Center", "settings": "System Settings", "profile": "Profile", "help": "Help Center", "support": "Technical Support"}, "dashboard": {"welcome": "Welcome Back", "overview": "Overview", "statistics": "Statistics", "recentActivities": "Recent Activities", "quickActions": "Quick Actions", "totalUsers": "Total Users", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "activeESIMs": "Active eSIMs", "monthlyGrowth": "Monthly Growth", "todayStats": "Today's Stats", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year"}, "user": {"users": "Users", "userManagement": "User Management", "userList": "User List", "userDetails": "User Details", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "userId": "User ID", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "phoneNumber": "Phone Number", "registrationDate": "Registration Date", "lastLogin": "Last Login", "userRole": "User Role", "userStatus": "User Status", "verified": "Verified", "unverified": "Unverified", "blocked": "Blocked", "suspended": "Suspended"}, "esim": {"esims": "eSIMs", "esimManagement": "eSIM Management", "esimList": "eSIM List", "esimDetails": "eSIM Details", "addESIM": "Add eSIM", "editESIM": "Edit eSIM", "deleteESIM": "Delete eSIM", "esimId": "eSIM ID", "iccid": "ICCID", "msisdn": "MSISDN", "imsi": "IMSI", "carrier": "Carrier", "country": "Country", "region": "Region", "dataPlans": "Data Plans", "validity": "Validity", "activationDate": "Activation Date", "expirationDate": "Expiration Date", "dataUsage": "Data Usage", "remainingData": "Remaining Data", "activated": "Activated", "expired": "Expired", "pending": "Pending"}, "order": {"orders": "Orders", "orderManagement": "Order Management", "orderList": "Order List", "orderDetails": "Order Details", "createOrder": "Create Order", "editOrder": "Edit Order", "deleteOrder": "Delete Order", "orderId": "Order ID", "orderNumber": "Order Number", "orderDate": "Order Date", "orderAmount": "Order Amount", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "shippingStatus": "Shipping Status", "customerInfo": "Customer Info", "orderItems": "Order Items", "pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "refunded": "Refunded", "paid": "Paid", "unpaid": "Unpaid", "partiallyPaid": "Partially Paid"}, "reseller": {"resellers": "Resellers", "resellerManagement": "Reseller Management", "resellerList": "Reseller List", "resellerDetails": "Reseller Details", "addReseller": "Add Reseller", "editReseller": "Edit Reseller", "deleteReseller": "Delete Reseller", "resellerId": "Reseller ID", "resellerName": "Reseller Name", "companyName": "Company Name", "contactPerson": "Contact Person", "contactEmail": "Contact Email", "contactPhone": "Contact Phone", "businessLicense": "Business License", "commissionRate": "Commission Rate", "creditLimit": "Credit Limit", "totalSales": "Total Sales", "pendingCommission": "Pending Commission", "paidCommission": "Paid Commission"}, "enterprise": {"enterprises": "Enterprises", "enterpriseManagement": "Enterprise Management", "enterpriseList": "Enterprise List", "enterpriseDetails": "Enterprise Details", "addEnterprise": "Add Enterprise", "editEnterprise": "Edit Enterprise", "deleteEnterprise": "Delete Enterprise", "enterpriseId": "Enterprise ID", "enterpriseName": "Enterprise Name", "industry": "Industry", "businessType": "Business Type", "contractPeriod": "Contract Period", "contractAmount": "Contract Amount", "serviceLevel": "Service Level", "accountManager": "Account Manager", "renewalDate": "Renewal Date"}, "promotion": {"promotions": "Promotions", "promotionManagement": "Promotion Management", "promotionList": "Promotion List", "promotionDetails": "Promotion Details", "addPromotion": "Add Promotion", "editPromotion": "Edit Promotion", "deletePromotion": "Delete Promotion", "promotionId": "Promotion ID", "promotionName": "Promotion Name", "promotionType": "Promotion Type", "discountType": "Discount Type", "discountValue": "Discount Value", "startDate": "Start Date", "endDate": "End Date", "usageLimit": "Usage Limit", "usedCount": "Used Count", "remainingCount": "Remaining Count", "couponCode": "Coupon Code", "percentage": "Percentage", "fixedAmount": "Fixed Amount"}, "rate": {"rates": "Rates", "rateManagement": "Rate Management", "rateList": "Rate List", "rateDetails": "Rate Details", "addRate": "Add Rate", "editRate": "Edit Rate", "deleteRate": "Delete Rate", "rateId": "Rate ID", "rateName": "Rate Name", "rateType": "Rate Type", "basePrice": "Base Price", "dataAllowance": "Data Allowance", "validityPeriod": "Validity Period", "roamingCharges": "Roaming Charges", "additionalServices": "Additional Services", "pricePerMB": "Price per MB", "pricePerGB": "Price per GB", "dailyRate": "Daily Rate", "weeklyRate": "Weekly Rate", "monthlyRate": "Monthly Rate"}, "credit": {"credits": "Credits", "creditManagement": "Credit Management", "creditList": "Credit List", "creditDetails": "Credit Details", "addCredit": "Add Credit", "editCredit": "Edit Credit", "deleteCredit": "Delete Credit", "creditId": "Credit ID", "creditBalance": "Credit Balance", "creditHistory": "Credit History", "creditTransaction": "Credit Transaction", "earned": "Earned", "spent": "Spent", "expired": "Expired", "pending": "Pending", "transactionDate": "Transaction Date", "transactionType": "Transaction Type", "transactionAmount": "Transaction Amount", "description": "Description", "referenceId": "Reference ID"}, "report": {"reports": "Reports", "reportCenter": "Report Center", "salesReport": "Sales Report", "userReport": "User Report", "revenueReport": "Revenue Report", "usageReport": "Usage Report", "performanceReport": "Performance Report", "generateReport": "Generate Report", "exportReport": "Export Report", "reportPeriod": "Report Period", "reportType": "Report Type", "dateRange": "Date Range", "customRange": "Custom Range", "lastWeek": "Last Week", "lastMonth": "Last Month", "lastQuarter": "Last Quarter", "lastYear": "Last Year"}, "settings": {"settings": "Settings", "systemSettings": "System Settings", "generalSettings": "General Settings", "securitySettings": "Security Settings", "notificationSettings": "Notification Settings", "emailSettings": "<PERSON><PERSON>s", "smsSettings": "SMS Settings", "paymentSettings": "Payment Settings", "apiSettings": "API Settings", "backupSettings": "Backup Settings", "maintenanceMode": "Maintenance Mode", "systemInfo": "System Info", "version": "Version", "lastUpdate": "Last Update", "serverStatus": "Server Status", "databaseStatus": "Database Status"}, "profile": {"profile": "Profile", "editProfile": "Edit Profile", "changePassword": "Change Password", "accountSettings": "Account <PERSON><PERSON>", "personalInfo": "Personal Info", "contactInfo": "Contact Info", "preferences": "Preferences", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "twoFactorAuth": "Two-Factor Authentication", "loginHistory": "Login History", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "language": "Language", "timezone": "Timezone", "theme": "Theme"}, "table": {"noData": "No Data", "noResults": "No matching results found", "itemsPerPage": "Items per page", "totalItems": "Total {count} items", "page": "Page {current} of {total}", "firstPage": "First", "lastPage": "Last", "prevPage": "Previous", "nextPage": "Next", "searchPlaceholder": "Enter keywords to search...", "filterBy": "Filter by {field}", "sortBy": "Sort by {field}", "selectRow": "Select row", "selectAllRows": "Select all rows", "selectedRows": "{count} rows selected"}, "form": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least {min} characters", "passwordsNotMatch": "Passwords do not match", "invalidFormat": "Invalid format", "valueTooSmall": "Value cannot be less than {min}", "valueTooLarge": "Value cannot be greater than {max}", "uploadFailed": "File upload failed", "fileTooLarge": "File size cannot exceed {size}", "unsupportedFileType": "Unsupported file type", "pleaseFillAllFields": "Please fill in all required fields", "dataWillBeLost": "Unsaved data will be lost. Are you sure you want to leave?"}, "notification": {"success": "Operation completed successfully", "error": "Operation failed", "warning": "Warning", "info": "Information", "saveSuccess": "Saved successfully", "deleteSuccess": "Deleted successfully", "updateSuccess": "Updated successfully", "createSuccess": "Created successfully", "deleteConfirm": "Are you sure you want to delete this record? This action cannot be undone.", "networkError": "Network connection error, please check your connection", "serverError": "Server error, please try again later", "permissionDenied": "Insufficient permissions to perform this operation", "sessionTimeout": "Session timeout, please login again"}}