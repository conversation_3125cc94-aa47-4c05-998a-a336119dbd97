'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function RatesPage() {
  const t = useTranslations('rate')

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('rateManagement')}</h1>
          <p className="text-muted-foreground">
            配置和管理系统费率结构
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            费率管理功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
