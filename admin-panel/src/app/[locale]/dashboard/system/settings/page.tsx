'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function SettingsPage() {
  const t = useTranslations('settings')

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('systemSettings')}</h1>
          <p className="text-muted-foreground">
            配置系统参数和全局设置
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            系统设置功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
