'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function ResellersPage() {
  const t = useTranslations('reseller')

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('resellerManagement')}</h1>
          <p className="text-muted-foreground">
            管理代理商账户和权限
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            代理商管理功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
