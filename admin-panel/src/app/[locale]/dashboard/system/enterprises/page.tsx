'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function EnterprisesPage() {
  const t = useTranslations('enterprise')

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('enterpriseManagement')}</h1>
          <p className="text-muted-foreground">
            管理企业客户和合作伙伴
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            企业管理功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
