'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { InternationalizedDashboard } from '@/components/business/internationalized-dashboard'
import { DashboardHeader } from '@/components/layout/global-header'

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        {/* 仪表板头部 */}
        <div className="border-b bg-card">
          <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
            <h1 className="text-2xl font-bold text-foreground">仪表板</h1>
            <DashboardHeader />
          </div>
        </div>
        
        {/* 主要内容 */}
        <div className="max-w-7xl mx-auto p-8">
          <InternationalizedDashboard />
        </div>
      </div>
    </ProtectedRoute>
  )
} 