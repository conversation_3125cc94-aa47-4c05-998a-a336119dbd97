'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function PromotionsPage() {
  const t = useTranslations('promotion')

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('promotionManagement')}</h1>
          <p className="text-muted-foreground">
            创建和管理促销活动和优惠券
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            促销管理功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
