import { ReactNode } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { AuthProvider } from '@/components/layout/AuthProvider'

interface DashboardLayoutProps {
  children: ReactNode
}

export default function Layout({ children }: DashboardLayoutProps) {
  return (
    <AuthProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </AuthProvider>
  )
}
