'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function ProfilePage() {
  const t = useTranslations('profile')

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('profile')}</h1>
          <p className="text-muted-foreground">
            管理您的个人资料和账户设置
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            个人资料功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
