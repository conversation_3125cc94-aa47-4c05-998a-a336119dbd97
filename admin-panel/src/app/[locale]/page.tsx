'use client'

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from '@/i18n/routing'
import { Loading } from '@/components/common'
export default function HomePage() {
  const t = useTranslations('common')
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        router.push('/dashboard')
      } else {
        router.push('/login')
      }
    }
  }, [isAuthenticated, isLoading, router])

  return (
    <Loading 
      text={t('loading')} 
      size="lg" 
      fullScreen 
    />
  )
}
