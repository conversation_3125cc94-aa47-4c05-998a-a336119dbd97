import { ReactNode } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { AuthProvider } from '@/components/layout/AuthProvider'

interface UsersLayoutProps {
  children: ReactNode
}

export default function UsersLayout({ children }: UsersLayoutProps) {
  return (
    <AuthProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </AuthProvider>
  )
}
