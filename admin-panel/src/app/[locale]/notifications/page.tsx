'use client'

import ProtectedRoute from '@/components/layout/ProtectedRoute'
import { useTranslations } from 'next-intl'

export default function NotificationsPage() {
  const t = useTranslations('navigation')

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('notifications')}</h1>
          <p className="text-muted-foreground">
            查看和管理系统通知
          </p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            通知中心功能正在开发中...
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
