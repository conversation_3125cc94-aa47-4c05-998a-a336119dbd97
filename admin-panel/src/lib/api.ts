import { LoginRequest, LoginResponse, User } from '@/types/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:18080'

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // 添加 Token 到请求头
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        let errorMessage = `Request failed: ${response.status}`
        let errorCode = ''
        
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorMessage
          errorCode = errorData.code || ''
        } catch {
          // 如果无法解析错误响应，使用默认错误消息
        }
        
        throw new ApiError(errorMessage, response.status, errorCode)
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Network request failed', 0)
    }
  }

  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }
}

// 创建全局 API 实例
export const api = new ApiClient()

// 认证相关 API
export const authApi = {
  login: (credentials: LoginRequest): Promise<LoginResponse> =>
    api.post('/api/v1/auth/login', credentials),
  
  logout: (): Promise<void> =>
    api.post('/api/v1/auth/logout'),
    
  refreshToken: (): Promise<{ token: string }> =>
    api.post('/api/v1/auth/refresh'),
    
  getProfile: (): Promise<User> =>
    api.get('/api/v1/users/me'),
}

export { ApiError } 