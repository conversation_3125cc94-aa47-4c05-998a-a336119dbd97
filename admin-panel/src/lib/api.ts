import { LoginRequest, LoginResponse, User } from '@/types/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:18080'

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // 添加 Token 到请求头
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        let errorMessage = `Request failed: ${response.status}`
        let errorCode = ''
        
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorMessage
          errorCode = errorData.code || ''
        } catch {
          // 如果无法解析错误响应，使用默认错误消息
        }
        
        throw new ApiError(errorMessage, response.status, errorCode)
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Network request failed', 0)
    }
  }

  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }
}

// 创建全局 API 实例
export const api = new ApiClient()

// 模拟数据用于开发测试
const mockUsers = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    name: '系统管理员',
    role: 'admin' as const,
    avatar: '',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  '<EMAIL>': {
    id: '2',
    email: '<EMAIL>',
    name: '代理商用户',
    role: 'reseller' as const,
    avatar: '',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>',
    name: '企业用户',
    role: 'enterprise' as const,
    avatar: '',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
}

// 认证相关 API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 简单的模拟认证逻辑
    const user = mockUsers[credentials.email as keyof typeof mockUsers]
    if (user && credentials.password === 'password') {
      const token = `mock-jwt-token-${user.id}-${Date.now()}`
      return { token, user }
    }

    throw new ApiError('Invalid credentials', 401)
  },

  logout: async (): Promise<void> => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  },

  refreshToken: async (): Promise<{ token: string }> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return { token: `refreshed-token-${Date.now()}` }
  },

  getProfile: async (): Promise<User> => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 从localStorage获取token来确定用户
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new ApiError('No token found', 401)
    }

    // 简单解析token获取用户ID（实际应用中应该验证JWT）
    const userId = token.includes('mock-jwt-token-1') ? '1' :
                   token.includes('mock-jwt-token-2') ? '2' :
                   token.includes('mock-jwt-token-3') ? '3' : null

    if (!userId) {
      throw new ApiError('Invalid token', 401)
    }

    const user = Object.values(mockUsers).find(u => u.id === userId)
    if (!user) {
      throw new ApiError('User not found', 404)
    }

    return user
  },
}

export { ApiError } 