import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthStore, LoginRequest, User } from '@/types/auth'
import { authApi, ApiError } from '@/lib/api'

const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      // 添加初始化标记，避免重复初始化
      _initialized: false,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApi.login(credentials)
          const { token, ...user } = response
          
          // 存储到 localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('auth_token', token)
          }
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          // 统一返回错误类型，让页面组件处理国际化
          let errorType = 'UNKNOWN_ERROR'
          
          if (error instanceof ApiError) {
            switch (error.status) {
              case 401:
                errorType = 'INVALID_CREDENTIALS'
                break
              case 403:
                errorType = 'ACCOUNT_DISABLED'
                break
              case 429:
                errorType = 'TOO_MANY_ATTEMPTS'
                break
              case 500:
                errorType = 'SERVER_ERROR'
                break
              default:
                errorType = 'UNKNOWN_ERROR'
            }
          } else if (error instanceof Error) {
            // 网络错误等
            errorType = 'NETWORK_ERROR'
          }
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorType,
          })
          
          throw error
        }
      },

      logout: () => {
        // 清除 localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token')
        }
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          _initialized: false, // 重置初始化标记
        })
      },

      setUser: (user: User) => {
        set({ user })
      },

      setToken: (token: string) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', token)
        }
        set({ token })
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      initializeAuth: async () => {
        const currentState = get()

        // 如果已经初始化过，直接返回
        if (currentState._initialized) {
          return
        }

        set({ _initialized: true, isLoading: true })

        if (typeof window !== 'undefined') {
          const token = localStorage.getItem('auth_token')
          if (token) {
            try {
              // 验证 token 并获取用户信息
              const user = await authApi.getProfile()
              set({
                token,
                user,
                isAuthenticated: true,
                isLoading: false,
              })
            } catch (error) {
              // Token 无效，清除认证状态
              localStorage.removeItem('auth_token')
              set({
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false,
                _initialized: true,
              })
            }
          } else {
            set({
              isLoading: false,
            })
          }
        } else {
          set({
            isLoading: false,
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        // 不持久化初始化标记和加载状态
      }),
      onRehydrateStorage: () => (state) => {
        // 重新水化后重置初始化状态
        if (state) {
          state._initialized = false
          state.isLoading = false
        }
      },
    }
  )
)

export default useAuthStore 