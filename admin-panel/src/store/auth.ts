import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthStore, LoginRequest, User } from '@/types/auth'
import { authApi, ApiError } from '@/lib/api'

const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true, // set to true to avoid infinite loop
      error: null,
      
      _initialized: false,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApi.login(credentials)
          const { token, ...user } = response
          
          // save to localStorage when window is defined
          if (typeof window !== 'undefined') {
            localStorage.setItem('auth_token', token)
          }
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          let errorType = 'UNKNOWN_ERROR'
          
          if (error instanceof ApiError) {
            switch (error.status) {
              case 401:
                errorType = 'INVALID_CREDENTIALS'
                break
              case 403:
                errorType = 'ACCOUNT_DISABLED'
                break
              case 429:
                errorType = 'TOO_MANY_ATTEMPTS'
                break
              case 500:
                errorType = 'SERVER_ERROR'
                break
              default:
                errorType = 'UNKNOWN_ERROR'
            }
          } else if (error instanceof Error) {
            // 网络错误等
            errorType = 'NETWORK_ERROR'
          }
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorType,
          })
          
          throw error
        }
      },

      logout: () => {
        // remove auth_token from localStorage when window is defined
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token')
        }
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          _initialized: true, // 保持 initialized 状态，避免重新初始化
        })
      },

      setUser: (user: User) => {
        set({ user })
      },

      setToken: (token: string) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', token)
        }
        set({ token })
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      initializeAuth: async () => {
        const currentState = get()

        // if already initialized, return
        if (currentState._initialized) {
          return
        }

        set({ isLoading: true })

        try {
          if (typeof window !== 'undefined') {
            const token = localStorage.getItem('auth_token')
            if (token) {
              // verify token and get user info
              const user = await authApi.getProfile()
              set({
                token,
                user,
                isAuthenticated: true,
                isLoading: false,
                _initialized: true,
              })
            } else {
              set({
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false,
                _initialized: true,
              })
            }
          } else {
            set({
              isLoading: false,
              _initialized: true,
            })
          }
        } catch (error) {
          // if token is invalid, clear authentication state
          if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token')
          }
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            _initialized: true,
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        // do not persist _initialized and isLoading
      }),
      onRehydrateStorage: () => (state) => {
        // keep _initialized to false to trigger reinitialization
        // but do not reset isLoading, let initializeAuth manage it
        if (state) {
          state._initialized = false
          // keep isLoading to true to avoid infinite loop
        }
      },
    }
  )
)

export default useAuthStore 