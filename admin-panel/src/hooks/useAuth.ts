import { useEffect } from 'react'
import { useRouter } from '@/i18n/routing'
import useAuthStore from '@/store/auth'
import { UserRole } from '@/types/auth'

export const useAuth = () => {
  const store = useAuthStore()
  return store
}

export const useRequireAuth = (redirectTo: '/login' | '/unauthorized' = '/login') => {
  // 分别调用选择器，避免创建新对象导致的无限循环
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)
  const isLoading = useAuthStore(state => state.isLoading)
  const _initialized = useAuthStore(state => state._initialized)
  const router = useRouter()

  useEffect(() => {
    // 只有在认证状态已初始化且不在加载中时才进行重定向
    if (_initialized && !isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [_initialized, isAuthenticated, isLoading, router, redirectTo])

  return { isAuthenticated, isLoading, _initialized }
}

export const useRequireRole = (
  allowedRoles: UserRole[],
  redirectTo: '/login' | '/unauthorized' = '/unauthorized'
) => {
  // 分别调用选择器，避免创建新对象导致的无限循环
  const user = useAuthStore(state => state.user)
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)
  const isLoading = useAuthStore(state => state.isLoading)
  const _initialized = useAuthStore(state => state._initialized)
  const router = useRouter()

  useEffect(() => {
    // 只有在认证状态已初始化且不在加载中时才进行重定向
    if (_initialized && !isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }
      
      // 只有当有角色要求时才检查角色
      if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
        router.push(redirectTo)
        return
      }
    }
  }, [_initialized, user, isAuthenticated, isLoading, allowedRoles, router, redirectTo])

  // 如果没有角色要求，则认为有权限
  const hasRole = allowedRoles.length === 0 ? true : (user ? allowedRoles.includes(user.role) : false)

  return {
    isAuthenticated,
    isLoading,
    hasRole,
    user,
    _initialized,
  }
}

export const useLogout = () => {
  const logout = useAuthStore(state => state.logout)
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  return handleLogout
} 