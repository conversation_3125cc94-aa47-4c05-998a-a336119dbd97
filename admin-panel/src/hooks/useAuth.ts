import { useEffect } from 'react'
import { useRouter } from '@/i18n/routing'
import useAuthStore from '@/store/auth'
import { UserRole } from '@/types/auth'

export const useAuth = () => {
  const store = useAuthStore()

  useEffect(() => {
    // 初始化认证状态，只在组件挂载时执行一次
    store.initializeAuth()
  }, []) // 移除store依赖，避免无限循环

  return store
}

export const useRequireAuth = (redirectTo: '/' | '/login' | '/dashboard' | '/unauthorized' = '/login') => {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, router, redirectTo])

  return { isAuthenticated, isLoading }
}

export const useRequireRole = (
  allowedRoles: UserRole[],
  redirectTo: '/' | '/login' | '/dashboard' | '/unauthorized' = '/unauthorized'
) => {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }
      
      // 只有当有角色要求时才检查角色
      if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
        router.push(redirectTo)
        return
      }
    }
  }, [user, isAuthenticated, isLoading, allowedRoles, router, redirectTo])

  // 如果没有角色要求，则认为有权限
  const hasRole = allowedRoles.length === 0 ? true : (user ? allowedRoles.includes(user.role) : false)

  return {
    isAuthenticated,
    isLoading,
    hasRole,
    user,
  }
}

export const useLogout = () => {
  const { logout } = useAuth()
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  return handleLogout
} 