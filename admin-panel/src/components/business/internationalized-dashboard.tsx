'use client'

import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LanguageSwitcher } from '@/components/layout/language-switcher'
import { formatLocalizedNumber, formatLocalizedDate, formatLocalizedCurrency } from '@/lib/i18n-utils'
import { type Locale } from '@/i18n/config'
import { Users, CreditCard, Activity, TrendingUp } from 'lucide-react'

export function InternationalizedDashboard() {
  const t = useTranslations()
  const locale = useLocale() as Locale

  // 示例数据
  const stats = {
    totalUsers: 12450,
    totalOrders: 3280,
    totalRevenue: 125680.50,
    activeESIMs: 8920
  }

  const currentDate = new Date()

  return (
    <div className="space-y-6">
      {/* 页面标题和语言切换 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('dashboard.welcome')}
          </h1>
          <p className="text-muted-foreground">
            {t('dashboard.overview')} - {formatLocalizedDate(currentDate, locale)}
          </p>
        </div>
        <LanguageSwitcher />
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.totalUsers')}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatLocalizedNumber(stats.totalUsers, locale)}
            </div>
            <p className="text-xs text-muted-foreground">
              +20.1% {t('dashboard.thisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.totalOrders')}
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatLocalizedNumber(stats.totalOrders, locale)}
            </div>
            <p className="text-xs text-muted-foreground">
              +15.3% {t('dashboard.thisWeek')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.totalRevenue')}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatLocalizedCurrency(stats.totalRevenue, locale)}
            </div>
            <p className="text-xs text-muted-foreground">
              +25.7% {t('dashboard.thisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.activeESIMs')}
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatLocalizedNumber(stats.activeESIMs, locale)}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.8% {t('dashboard.thisWeek')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 功能演示卡片 */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('navigation.esim')}</CardTitle>
            <CardDescription>
              {t('esim.esimManagement')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>{t('esim.activated')}:</span>
                <span className="font-semibold text-green-600">
                  {formatLocalizedNumber(5680, locale)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('esim.pending')}:</span>
                <span className="font-semibold text-yellow-600">
                  {formatLocalizedNumber(230, locale)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('esim.expired')}:</span>
                <span className="font-semibold text-red-600">
                  {formatLocalizedNumber(145, locale)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('navigation.orders')}</CardTitle>
            <CardDescription>
              {t('order.orderManagement')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>{t('order.completed')}:</span>
                <span className="font-semibold text-green-600">
                  {formatLocalizedNumber(2890, locale)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('order.processing')}:</span>
                <span className="font-semibold text-blue-600">
                  {formatLocalizedNumber(125, locale)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('order.cancelled')}:</span>
                <span className="font-semibold text-red-600">
                  {formatLocalizedNumber(65, locale)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 多语言特性说明 */}
      <Card>
        <CardHeader>
          <CardTitle>🌍 国际化功能演示</CardTitle>
          <CardDescription>
            当前语言: {locale} - 所有文本、数字、日期和货币格式都已本地化
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <h4 className="font-semibold mb-2">数字格式</h4>
              <p className="text-sm text-muted-foreground">
                {formatLocalizedNumber(1234567.89, locale)}
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">日期格式</h4>
              <p className="text-sm text-muted-foreground">
                {formatLocalizedDate(currentDate, locale, { 
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">货币格式</h4>
              <p className="text-sm text-muted-foreground">
                {formatLocalizedCurrency(12345.67, locale)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 