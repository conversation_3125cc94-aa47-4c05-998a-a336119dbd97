"use client"

import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import {
  LayoutDashboard,
  Users,
  Smartphone,
  ShoppingCart,
  Gift,
  CreditCard,
  Settings,
  Building2,
  UserCheck,
  DollarSign,
  BarChart3,
  Bell,
  User,
  LogOut,
  ChevronUp
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from '@/components/ui/sidebar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useAuth } from '@/hooks/useAuth'

// 导航菜单配置
const navigationItems = [
  {
    title: 'dashboard',
    icon: LayoutDashboard,
    href: '/dashboard',
    roles: ['admin', 'reseller', 'enterprise']
  },
  {
    title: 'users',
    icon: Users,
    href: '/dashboard/users',
    roles: ['admin', 'reseller', 'enterprise']
  },
  {
    title: 'esims',
    icon: Smartphone,
    href: '/dashboard/esims',
    roles: ['admin', 'reseller', 'enterprise']
  },
  {
    title: 'orders',
    icon: ShoppingCart,
    href: '/dashboard/orders',
    roles: ['admin', 'reseller', 'enterprise']
  },
  {
    title: 'promotions',
    icon: Gift,
    href: '/dashboard/promotions',
    roles: ['admin', 'reseller', 'enterprise']
  },
  {
    title: 'finance',
    icon: CreditCard,
    href: '/dashboard/finance',
    roles: ['admin', 'reseller', 'enterprise']
  }
]

const systemItems = [
  {
    title: 'rates',
    icon: DollarSign,
    href: '/dashboard/system/rates',
    roles: ['admin']
  },
  {
    title: 'resellers',
    icon: UserCheck,
    href: '/dashboard/system/resellers',
    roles: ['admin']
  },
  {
    title: 'enterprises',
    icon: Building2,
    href: '/dashboard/system/enterprises',
    roles: ['admin']
  },
  {
    title: 'analytics',
    icon: BarChart3,
    href: '/dashboard/system/analytics',
    roles: ['admin']
  },
  {
    title: 'settings',
    icon: Settings,
    href: '/dashboard/system/settings',
    roles: ['admin']
  }
]

export function AppSidebar() {
  const t = useTranslations('navigation')
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const { isMobile } = useSidebar()

  // 根据用户角色过滤菜单项
  const filterMenuByRole = (items: typeof navigationItems) => {
    if (!user?.role) return []
    return items.filter(item => item.roles.includes(user.role))
  }

  const filteredNavigation = filterMenuByRole(navigationItems)
  const filteredSystemItems = filterMenuByRole(systemItems)

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/dashboard">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Smartphone className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">AuraESIM</span>
                  <span className="truncate text-xs">管理后台</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {/* 主要功能 */}
        <SidebarGroup>
          <SidebarGroupLabel>{t('main')}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredNavigation.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton asChild isActive={pathname === item.href}>
                    <Link href={item.href}>
                      <item.icon />
                      <span>{t(item.title)}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* 系统管理 - 仅管理员可见 */}
        {filteredSystemItems.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel>{t('system')}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {filteredSystemItems.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild isActive={pathname === item.href}>
                      <Link href={item.href}>
                        <item.icon />
                        <span>{t(item.title)}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user?.avatar} alt={user?.name} />
                    <AvatarFallback className="rounded-lg">
                      {user?.name?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{user?.name}</span>
                    <span className="truncate text-xs">{user?.email}</span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/profile">
                    <User />
                    {t('profile')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/notifications">
                    <Bell />
                    {t('notifications')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={logout}>
                  <LogOut />
                  {t('logout')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
