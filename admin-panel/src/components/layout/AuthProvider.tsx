'use client'

import { useEffect } from 'react'
import useAuthStore from '@/store/auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const initializeAuth = useAuthStore(state => state.initializeAuth)

  useEffect(() => {
    // 应用启动时初始化认证状态
    initializeAuth()
  }, [initializeAuth])

  return <>{children}</>
}

export default AuthProvider