'use client'

import { useMemo } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { UserRole } from '@/types/auth'
import { useRouter } from '@/i18n/routing'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
  requiredRole?: UserRole
  fallback?: React.ReactNode
}

export default function ProtectedRoute({
  children,
  allowedRoles,
  requiredRole,
  fallback = <div className="flex items-center justify-center min-h-screen">Loading...</div>
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // 使用 useMemo 稳定化 allowedRoles 数组
  const stableAllowedRoles = useMemo(() => {
    if (requiredRole) {
      return [requiredRole]
    }
    return allowedRoles || []
  }, [allowedRoles, requiredRole])

  const hasRoleRequirement = Boolean((allowedRoles && allowedRoles.length > 0) || requiredRole)

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }

      // 检查角色权限
      if (hasRoleRequirement && user && !stableAllowedRoles.includes(user.role)) {
        router.push('/dashboard') // 重定向到dashboard而不是unauthorized
        return
      }
    }
  }, [isAuthenticated, isLoading, user, hasRoleRequirement, stableAllowedRoles, router])

  if (isLoading) {
    return <>{fallback}</>
  }
  
  if (!isAuthenticated) {
    return null // 重定向已在useEffect中处理
  }

  if (hasRoleRequirement && user && !stableAllowedRoles.includes(user.role)) {
    return null // 重定向已在useEffect中处理
  }

  return <>{children}</>
}