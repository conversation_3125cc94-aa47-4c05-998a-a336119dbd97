'use client'

import { useMemo } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { UserRole } from '@/types/auth'
import { useRouter } from '@/i18n/routing'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
  requiredRole?: UserRole
  fallback?: React.ReactNode
}

export default function ProtectedRoute({
  children,
  allowedRoles,
  requiredRole,
  fallback = <div className="flex items-center justify-center min-h-screen">Loading...</div>
}: ProtectedRouteProps) {
  // 分别调用选择器，避免创建新对象导致的无限循环
  const user = useAuth().user
  const isAuthenticated = useAuth().isAuthenticated
  const isLoading = useAuth().isLoading
  const _initialized = useAuth()._initialized
  const router = useRouter()

  // 使用 useMemo 稳定化 allowedRoles 数组
  const stableAllowedRoles = useMemo(() => {
    if (requiredRole) {
      return [requiredRole]
    }
    return allowedRoles || []
  }, [allowedRoles, requiredRole])

  const hasRoleRequirement = Boolean((allowedRoles && allowedRoles.length > 0) || requiredRole)

  useEffect(() => {
    // 只有在认证状态已初始化且不在加载中时才进行重定向
    if (_initialized && !isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }

      // 检查角色权限
      if (hasRoleRequirement && user && !stableAllowedRoles.includes(user.role)) {
        router.push('/dashboard') // 重定向到dashboard而不是unauthorized
        return
      }
    }
  }, [_initialized, isAuthenticated, isLoading, user, hasRoleRequirement, stableAllowedRoles, router])

  // 在认证状态未初始化或正在加载时显示fallback
  if (!_initialized || isLoading) {
    return <>{fallback}</>
  }
  
  if (!isAuthenticated) {
    return null // 重定向已在useEffect中处理
  }

  if (hasRoleRequirement && user && !stableAllowedRoles.includes(user.role)) {
    return null // 重定向已在useEffect中处理
  }

  return <>{children}</>
}