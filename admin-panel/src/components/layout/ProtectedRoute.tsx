'use client'

import { useMemo } from 'react'
import { useRequireAuth, useRequireRole } from '@/hooks/useAuth'
import { UserRole } from '@/types/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
  requiredRole?: UserRole
  fallback?: React.ReactNode
}

export default function ProtectedRoute({
  children,
  allowedRoles,
  requiredRole,
  fallback = <div>Loading...</div>
}: ProtectedRouteProps) {
  // 使用 useMemo 稳定化 allowedRoles 数组，避免不必要的重新渲染
  const stableAllowedRoles = useMemo(() => {
    if (requiredRole) {
      return [requiredRole]
    }
    return allowedRoles || []
  }, [allowedRoles, requiredRole])
  const hasRoleRequirement = Boolean((allowedRoles && allowedRoles.length > 0) || requiredRole)
  
  // 始终调用基础认证检查
  const authResult = useRequireAuth()
  
  // 始终调用角色检查，但传入稳定的数组
  const roleResult = useRequireRole(stableAllowedRoles)
  
  // 根据是否有角色要求来决定使用哪个结果
  const { isAuthenticated, isLoading } = hasRoleRequirement ? roleResult : authResult
  const hasRole = hasRoleRequirement ? roleResult.hasRole : true
  
  if (isLoading) {
    return <>{fallback}</>
  }
  
  if (!isAuthenticated || (hasRoleRequirement && !hasRole)) {
    return null // 重定向将在 hook 中处理
  }
  
  return <>{children}</>
} 